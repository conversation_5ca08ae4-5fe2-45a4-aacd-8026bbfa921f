import { TMap, GameSeting } from "GameSeting";
import { ListenID } from "ListenID";
import { GameatrCfg, GameatrDefine } from "GameatrCfg";
import { ObjectPool } from "ObjectPool";
import { Manager } from "Manager";
import { Game } from "Game";

const { ccclass } = cc._decorator;

export namespace Property {
    const t = new TMap();
    
    export const DefaultData = {
        hp: 0,
        def: 0,
        speed: 0,
        atk: 1,
        atkArea: 100,
        atkSpeed: 1,
        atkInterval: 1,
        MissRatio: 0,
        crit: 0.01,
        CritNum: 2,
        Picking: 100,
        ExpUp: 1,
        Scale: 1
    };

    export class Vo {
        public ower: any;
        public base: any;
        public cut: any;
        public extra: any;

        constructor(owner: any, data: any = DefaultData) {
            if (data) {
                this.ower = owner;
                this.base = Object.assign({}, DefaultData);
                this.cut = Object.assign({}, DefaultData);
                this.extra = Object.assign({}, DefaultData);
                
                for (let key in this.extra) {
                    this.extra[key] = 0;
                }
                
                this.set(data);
                owner.hurtMgr = new Hurt.Mgr(owner);
            }
        }

        public set(data: any = DefaultData): this {
            if (!data) {
                return this;
            }
            
            for (let key in data) {
                if (this.base[key] != null) {
                    this.base[key] = data[key];
                    this.cut[key] = data[key];
                }
            }
            
            return this;
        }

        public updateVo(): void {
            const attrMap = this.ower.buffMgr?.attrMapAll || t;
            
            this.cut.speed = Math.max((this.base.speed + this.extra.speed) * (1 + attrMap.getor(GameatrDefine.movespeed, 0)), 0);
            this.cut.hp = (this.base.hp + this.extra.hp) * (1 + attrMap.getor(GameatrDefine.hp, 0));
            this.cut.atk = (this.base.atk + this.extra.atk) * (1 + attrMap.getor(GameatrDefine.roleatk, 0));
            this.cut.atk = (this.base.atk + this.extra.atk + attrMap.getor(GameatrDefine.roleatkval, 0)) * (1 + attrMap.getor(GameatrDefine.roleatk, 0));
            this.cut.crit = this.base.crit + this.extra.crit + attrMap.getor(GameatrDefine.cirtrate, 0);
            this.cut.CritNum = (this.base.CritNum + this.extra.CritNum) * (1 + attrMap.getor(GameatrDefine.cirtdam, 0));
            
            this.ower.maxSpeed = this.cut.speed;
            this.ower.node.scale = this.ower.settingScale * (1 + attrMap.getor(GameatrDefine.modeScale, 0));
        }
    }
}

export namespace Hurt {
    export enum Type {
        Default = 1,
        Block = 2,
        Miss = 3,
        Critical = 4,
        Slash = 5
    }

    export const Pool = new ObjectPool(() => {
        return new Data();
    });

    export class Data extends GameSeting.CompBase {
        public static HurtBaseID: number = 0;
        public static CloneType: string[] = ["hid", "baseVal", "critVal", "critRate", "critValRate", "hurCd", "hitBack", "owner", "ownerSkill"];

        public val: number = 0;
        public isCrit: boolean = false;
        public hid: number = 1;
        public baseVal: number = 0;
        public extraVal: number = 0;
        public critValRate: number = 1;
        public critRate: number = 0;
        public hurCd: number = 0;
        public hitBack: number = 0;
        public owner: any = null;
        public ownerSkill: any = null;
        public hasBehitEffect: boolean = true;
        public hitPos: cc.Vec2 = cc.v2();
        public type: Type;

        constructor(data?: any) {
            super();
            if (data) {
                this.set(data);
            }
        }

        public unuse(): void {}

        public reuse(...args: any[]): void {}

        public destroy(): void {}

        public set(data: any): this {
            this.critRate = 0;
            this.hurCd = 0;
            this.hasBehitEffect = true;
            
            for (let key in data) {
                this[key] = data[key];
            }
            
            this.val = data.baseVal;
            this.hid = Data.HurtBaseID++;
            this.type = data.type || Type.Default;
            
            if (Data.HurtBaseID > 50000000000) {
                Data.HurtBaseID = 1;
            }
            
            return this;
        }

        public clone(other: Data): void {
            for (let i = 0; i < Data.CloneType.length; i++) {
                this[Data.CloneType[i]] = other[Data.CloneType[i]];
            }
        }

        public get isSlash(): boolean {
            return this.type === Type.Slash;
        }
    }

    const tempVec = cc.Vec2.ZERO;

    export class Mgr {
        public list: { [key: number]: number } = {};
        public owner: any = null;

        constructor(owner: any) {
            this.owner = owner;
        }

        public checkHurt(hurtData: Data): any {
            if (this.owner.isInvincible) {
                return null;
            }
            
            if (hurtData.hurCd === 0) {
                return this.onHurt(hurtData);
            } else {
                if (this.list[hurtData.hid]) {
                    return null;
                } else {
                    this.list[hurtData.hid] = hurtData.hurCd;
                    return this.onHurt(hurtData);
                }
            }
        }

        public onHurt(hurtData: Data): any {
            let extraDamageRate = 0;
            hurtData.extraVal = 0;
            hurtData.isCrit = Game.weightFloat(hurtData.critRate);
            
            if (hurtData.isCrit) {
                hurtData.type = Type.Critical;
            }

            if (hurtData.owner.isActive && hurtData.owner.buffMgr) {
                if (this.owner.curHpProgress <= 0.3) {
                    extraDamageRate += hurtData.owner.buffMgr.attrMapAll.getor(GameatrDefine.slayingDamage_30, 0);
                }
                
                cc.Vec2.subtract(tempVec, hurtData.owner.position, this.owner.position);
                const distanceSqr = tempVec.magSqr();
                
                if (hurtData.ownerSkill && hurtData.owner.buffMgr.attrBuffMap.getor(GameatrDefine.damdis, [])) {
                    hurtData.owner.buffMgr.attrBuffMap.getor(GameatrDefine.damdis, []).forEach((buff: any) => {
                        if (buff.isSpecificSkill && !buff.isSpecificSkill.includes(hurtData.ownerSkill.skillMainID)) {
                            return;
                        }
                        if (distanceSqr >= Math.pow(hurtData.ownerSkill.dis, 2) * buff.otherValue[0]) {
                            extraDamageRate += buff.attrMap.getor(GameatrDefine.damdis, 0);
                        }
                    });
                }

                const beheadedBuffs = hurtData.ownerSkill?.skillBuffItem.filter((item: any) => {
                    return item.attrMap.get(GameatrDefine.beheaded);
                });
                
                beheadedBuffs?.forEach((buff: any) => {
                    const specialData = buff.specialMap.find((item: any) => item.type === GameatrDefine.beheaded).data;
                    if (this.owner._curHp - (hurtData.baseVal + hurtData.baseVal * extraDamageRate) <= this.owner._curHp * specialData[0]) {
                        if (buff.cutVo.weight >= Math.random()) {
                            this.owner.addBuff(specialData[1]).setCaster(hurtData.owner);
                        }
                    }
                });
            }

            if (this.owner.buffMgr) {
                if (this.owner.buffMgr.isHasAttr(GameatrDefine.shieldBlock) && 
                    Game.weightFloat(this.owner.buffMgr.getAttr(GameatrDefine.shieldBlock, 0))) {
                    hurtData.val = 0;
                    hurtData.type = Type.Block;
                }
                
                extraDamageRate += this.owner.buffMgr.attrMapAll.getor(GameatrDefine.deepHurt, 0);
                
                const resistBuff = this.owner.buffMgr.attrBuffMap.get(GameatrDefine.resistDam)?.[0];
                if (resistBuff) {
                    resistBuff.unuseLayer();
                    return null;
                }
            }

            hurtData.extraVal += hurtData.baseVal * extraDamageRate;
            const finalDamage = (hurtData.isCrit ? hurtData.critValRate : 1) * (hurtData.baseVal + hurtData.extraVal);
            hurtData.val = Math.ceil(finalDamage);
            
            if (hurtData.owner.isActive) {
                hurtData.owner.node.emit(ListenID.Fight_SpawnHurt, hurtData, this.owner);
            }
            
            if (hurtData.ownerSkill?.cutVo.soundhitId) {
                Manager.audio.playAudio(hurtData.ownerSkill.cutVo.soundhitId);
            }

            return {
                type: hurtData.type
            };
        }

        public onUpdate(deltaTime: number): void {
            for (let key in this.list) {
                this.list[key] -= deltaTime;
                if (this.list[key] <= 0) {
                    delete this.list[key];
                }
            }
        }
    }
}
