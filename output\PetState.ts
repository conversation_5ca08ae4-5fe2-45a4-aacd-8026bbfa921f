import { ListenID } from "ListenID";
import { Notifier } from "Notifier";
import { StateMachine } from "StateMachine";
import { GameUtil } from "GameUtil";
import { Game } from "Game";

const tempVec1 = cc.v2();
const tempVec2 = cc.v2();
const tempVec3 = cc.v2();
const tempVec4 = cc.v2();
const tempVec5 = cc.v2();
const tempVec6 = cc.v2();

export namespace PetState {
    export class IdleState extends StateMachine.State.BaseModel {
        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.IDLE;
        }

        public onEnter(owner: any): void {
            super.onEnter(owner);
            owner.playAction("idle", true);
        }

        public onUpdate(owner: any, deltaTime: number): void {
            super.onUpdate(owner, deltaTime);
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }

    export class WanderState extends StateMachine.State.BaseModel {
        private _patrolAngle: cc.Vec2 = cc.Vec2.ZERO;
        private _patrolPos: cc.Vec2 = cc.Vec2.ZERO;
        private _time: number = 0;
        private _nextTiem: number = 2;

        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.WANDER;
        }

        public onEnter(owner: any): void {
            super.onEnter(owner);
            this.changeAngle();
        }

        private changeAngle(): void {
            this._time = 0;
            if (this._owner.targetGap > this._owner.followGap) {
                this._owner.toFollow();
            } else {
                this._patrolPos = GameUtil.AngleAndLenToPos(
                    GameUtil.random(0, 360), 
                    GameUtil.random(200, 1.5 * this._owner.followGap)
                );
                this._patrolAngle = this._patrolPos.normalize();
                this._nextTiem = GameUtil.random(1, 4);
            }
        }

        public onUpdate(owner: any, deltaTime: number): void {
            super.onUpdate(owner, deltaTime);
            if (cc.Vec2.squaredDistance(owner.steering.curTarget, owner.position) < Math.pow(50, 2)) {
                return;
            }
            this.onSteerMove(owner, deltaTime);
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }

    export class FollowState extends StateMachine.State.BaseModel {
        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.FOLLOW;
        }

        public onEnter(owner: any): void {
            super.onEnter(owner);
        }

        public onUpdate(owner: any, deltaTime: number): void {
            if (!this._owner.isBanMove) {
                super.onUpdate(owner, deltaTime);
                if (this._owner.maxSpeed !== 0) {
                    this.onSteerMove(owner, deltaTime);
                }
            }
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }

    export class AttackState extends StateMachine.State.BaseModel {
        private _curattackDelta: number = 0;
        private _attackPoint: number = 0;

        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.ATTACK;
        }

        public onEnter(owner: any): void {
            super.onEnter(owner);
            this._curattackDelta = 0;
            this._attackPoint = 0.5;
            this._owner.isBanMove;
        }

        public onUpdate(owner: any, deltaTime: number): void {
            super.onUpdate(owner, deltaTime);
            this._curattackDelta += deltaTime;
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }

    export class DeadState extends StateMachine.State.BaseModel {
        private _deltaTime: number = 0;

        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.DEAD;
        }

        public onEnter(owner: any): void {
            super.onEnter(owner);
            owner.node.emit(ListenID.Fight_Dead);
            this._deltaTime = 0.2;
            owner.isActive = false;
            Game.Mgr.instance.showEntityDieEffect(2, {
                position: owner.position,
                scale: 1
            });
        }

        public onUpdate(owner: any, deltaTime: number): void {
            super.onUpdate(owner, deltaTime);
            if (this._deltaTime >= 0 && !owner.isDead) {
                this._deltaTime -= deltaTime;
                cc.Vec2.set(tempVec1, -owner.heading.x, -owner.heading.y);
                cc.Vec2.multiplyScalar(tempVec1, tempVec1, 50 * deltaTime);
                cc.Vec2.add(tempVec1, tempVec1, owner.position);
                owner.setPosition(tempVec1);
                
                if (this._deltaTime <= 0) {
                    Notifier.send(ListenID.Fight_PetDead, owner);
                    owner.isDead = true;
                }
            }
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }
}
