import { BottomBarController } from "./BottomBarController";
import { FightController } from "./FightController";
import { BuffController } from "./BuffController";
import { SkillController } from "./SkillController";
import { LoadingController } from "./LoadingController";
import { SettingController } from "./SettingController";
import { ModeBackpackHeroController } from "./ModeBackpackHeroController";
import { ItemController } from "./ItemController";
import { GuidesController } from "./GuidesController";
import { ShopController } from "./ShopController";
import { PayController } from "./PayController";
import { ADController } from "./ADController";
import { RBadgeController } from "./RBadgeController";
import { ModeThrowingKnifeController } from "./ModeThrowingKnifeController";
import { ModePickUpBulletsController } from "./ModePickUpBulletsController";
import { ModeBulletsReboundController } from "./ModeBulletsReboundController";
import { ModeChainsController } from "./ModeChainsController";
import { TideDefendController } from "./TideDefendController";
import { ModeManGuardsController } from "./ModeManGuardsController";
import { ModeAllOutAttackController } from "./ModeAllOutAttackController";
import { ModeDragonWarController } from "./ModeDragonWarController";

export const ModuleLauncher = (): void => {
    new RBadgeController();
    new LoadingController();
    new GuidesController();
    new SettingController();
    new ItemController();
    new ShopController();
    new PayController();
    new ADController();
    new BottomBarController();
    new FightController();
    new SkillController();
    new BuffController();
    new ModeBackpackHeroController();
    new ModeThrowingKnifeController();
    new ModePickUpBulletsController();
    new ModeBulletsReboundController();
    new ModeChainsController();
    new TideDefendController();
    new ModeManGuardsController();
    new ModeAllOutAttackController();
    new ModeDragonWarController();
};
