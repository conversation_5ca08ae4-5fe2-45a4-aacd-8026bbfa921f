import { Notifier } from "Notifier";
import { StateMachine } from "StateMachine";
import { ListenID } from "ListenID";
import { GameUtil } from "GameUtil";

const tempVec = cc.v2();
const tempVec2 = cc.v2();
const tempVec3 = cc.v2();
const tempVec4 = cc.v2();
const tempVec5 = cc.v2();
const tempVec6 = cc.v2();

export namespace RoleState {
    export class IdleState extends StateMachine.State.BaseModel {
        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.IDLE;
        }

        public onEnter(owner: any): void {
            super.onEnter(owner);
            owner.playAction("idle", true);
            owner.velocity.set(cc.Vec2.ZERO);
        }

        public onUpdate(owner: any, deltaTime: number): void {
            super.onUpdate(owner, deltaTime);
            const readySkills = owner.skillMgr.getReadySkill();
            if (readySkills.length > 0) {
                this._parentState.changeState(StateMachine.State.Type.SKILL, readySkills[0].id);
            }
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }

    export class MoveState extends StateMachine.State.BaseModel {
        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.MOVE;
        }

        public onEnter(owner: any): void {
            super.onEnter(owner);
        }

        public onUpdate(owner: any, deltaTime: number): void {
            super.onUpdate(owner, deltaTime);
            cc.Vec2.multiplyScalar(tempVec, owner.velocity, deltaTime);
            cc.Vec2.add(tempVec, tempVec, owner.position);
            owner.updateDir(deltaTime);
            owner.setPosition(tempVec);
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }

    export class AttackState extends StateMachine.State.BaseModel {
        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.ATTACK;
        }

        public onEnter(owner: any): void {
            super.onEnter(owner);
        }

        public onUpdate(owner: any, deltaTime: number): void {
            super.onUpdate(owner, deltaTime);
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }

    export class DeadState extends StateMachine.State.BaseModel {
        private _deltaTime: number = 0;
        private _isShowView: boolean = false;

        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.DEAD;
        }

        public onEnter(owner: any): void {
            super.onEnter(owner);
            owner.mySkeleton.clearTracks();
            owner.setAnimation("die");
            this._deltaTime = 0.5;
            owner.isDead = true;
            this._isShowView = false;
        }

        public onUpdate(owner: any, deltaTime: number): void {
            super.onUpdate(owner, deltaTime);
            if (!this._isShowView) {
                this._deltaTime -= deltaTime;
                if (this._deltaTime <= 0) {
                    if (owner.isCanRelive) {
                        owner.isCanRelive = false;
                        Notifier.send(ListenID.Fight_OpenReliveView);
                    } else {
                        Notifier.send(ListenID.Fight_End);
                    }
                    this._isShowView = true;
                }
            }
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }

    export class SkillState extends StateMachine.State.BaseModel {
        public get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.SKILL;
        }

        public onEnter(owner: any, skillId?: any): void {
            super.onEnter(owner);
            const randomAttack = GameUtil.getRandomInArray(["attack", "attack2", "attack3"])[0];
            owner.setAnimation(randomAttack);
            owner.skillMgr.use(skillId);
        }

        public onExit(owner: any): void {
            super.onExit(owner);
        }
    }
}
