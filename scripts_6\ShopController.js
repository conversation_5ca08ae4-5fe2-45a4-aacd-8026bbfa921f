var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ShopController = undefined;
var $2CallID = require("CallID");
var $2ListenID = require("ListenID");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Time = require("Time");
var $2ShopModel = require("ShopModel");
var exp_ShopController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.setup($2ShopModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {
    this._model.reset();
  };
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "ShopController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Login_Finish, this.onLoginFinish, this, 200);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Shop_ShopItemList, this.onShop_ShopItemList, this);
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Shop_GetInfo, this.getShopInfo, this);
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Shop_GetItem, this.getShopItem, this);
  };
  _ctor.prototype.onLoginFinish = function () {
    this.mode.loginFinish();
    this.getRiotShopInfo();
    $2Notifier.Notifier.call($2CallID.CallID.Shop_GetProductList);
    $2Time.Time.delay(2, function () {
      $2Notifier.Notifier.call($2CallID.CallID.Shop_GetProductList);
    });
  };
  _ctor.prototype.onShop_ShopItemList = function (e) {
    var t;
    this.mode.payList = e;
    null === (t = this.mode.payList) || undefined === t || t.forEach(function (e) {
      e.formPrice = e.formattedPrice;
    });
    $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_Item);
  };
  _ctor.prototype.getMode = function () {
    return this._model;
  };
  _ctor.prototype.getRiotShopInfo = function () {
    return new Promise(function () {});
  };
  _ctor.prototype.getShopInfo = function () {};
  _ctor.prototype.getShopItem = function () {};
  _ctor.prototype.onCharge = function (e) {
    this._model.onBuyChange(e.id);
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.ShopController = exp_ShopController;