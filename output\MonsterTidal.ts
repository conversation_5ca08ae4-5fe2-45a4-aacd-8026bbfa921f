import { StateMachine } from "./StateMachine";
import { Game } from "./Game";
import { SkillManager } from "./SkillManager";
import { MonsterState } from "./MonsterState";
import { MonsterTidalState } from "./MonsterTidalState";
import { Monster } from "./Monster";

const { ccclass } = cc._decorator;

@ccclass
class MonsterTidal extends Monster {
    get game(): any {
        return Game.mgr;
    }

    public init(): void {
        super.init();
        this.stateMachine = new StateMachine(this);
        this.stateMachine.setCurrentState(MonsterTidalState.MonsterTidalGlobalState.instance);
        this.stateMachine.setGlobalState(MonsterTidalState.MonsterTidalGlobalState.instance);
        this.stateMachine.changeState(MonsterTidalState.MonsterTidalIdleState.instance);
        
        this.skillMgr = new SkillManager(this);
        this.monCfg?.skill?.forEach((skillId: number) => {
            this.skillMgr.addSkill(skillId);
        });
    }

    public onUpdate(dt: number): void {
        super.onUpdate(dt);
        this.skillMgr?.onUpdate(dt);
    }

    public toDead(): void {
        this.skillMgr?.clear();
        super.toDead();
    }

    public aiFire(): void {
        if (this.skillMgr && this.skillMgr.skills.length > 0) {
            const availableSkills = this.skillMgr.skills.filter((skill: any) => skill.isReady);
            if (availableSkills.length > 0) {
                const randomSkill = availableSkills[Math.floor(Math.random() * availableSkills.length)];
                this.skillMgr.use(randomSkill.skillCfg.id);
            }
        } else {
            super.aiFire();
        }
    }
}

export default MonsterTidal;
