import { ListenID } from "ListenID";
import { Cfg } from "Cfg";
import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { NotifyID } from "NotifyID";
import { Manager } from "Manager";
import { AlertManager } from "AlertManager";
import PayModel from "PayModel";

declare const wonderSdk: any;

export class PayController extends MVC.MController {
    private _curParam: any = null;
    private _curOrderId: string;
    private _curProductId: string;
    private _curPayCall: Function;

    constructor() {
        super();
        this.setup(PayModel.getInstance);
        this.changeListener(true);
    }

    public reset(): void {}

    public get classname(): string {
        return "PayController";
    }

    public registerAllProtocol(): void {}

    public changeListener(isAdd: boolean): void {
        Notifier.changeListener(isAdd, ListenID.Pay_ToPay, this.toPay, this);
        cc.game.on("payFinish", this.payFinish, this);
    }

    public setLoadingViewVisible(visible: boolean, type: number = 7): void {
        // Loading view logic
    }

    public async toPay(payData: any, callback: Function, extraParams: any): Promise<void> {
        this._curOrderId = null;
        this._curProductId = payData.productId;
        this._curParam = extraParams;

        if (wonderSdk.isByteDance && (cc.sys.os === cc.sys.OS_IOS || cc.sys.os === cc.sys.OS_OSX)) {
            AlertManager.showNormalTipsOnce("暂不支持，请使用安卓手机充值");
            return;
        }

        if (!wonderSdk.isNative && !wonderSdk.isWeChat && !wonderSdk.isByteDance) {
            if (callback) {
                callback(1);
            }
            return;
        }

        this._curPayCall = callback;
        this._curOrderId = "";
        Notifier.send(NotifyID.Game_LoadingView, true);

        const price = wonderSdk.isTest ? 1 : +payData.buyValue;

        if (payData.type === 1) {
            wonderSdk.toPay({
                orderId: "",
                price: price,
                goodsId: payData.productId,
                goodsName: payData.name,
                roleId: "",
                extraParams: extraParams
            });
        } else {
            wonderSdk.toSubscribe({
                orderId: "",
                price: price,
                goodsId: payData.productId,
                goodsName: payData.name,
                roleId: "",
                extraParams: extraParams
            });
        }
    }

    public payFinish(result: number, productId: string): void {
        console.log("payFinish-payController", result, productId);
        Notifier.send(NotifyID.Game_LoadingView, false);

        if (result === -1 || result === 0) {
            if (this._curPayCall) {
                this._curPayCall(result);
            }
            if (result === 0) {
                AlertManager.showNormalTips("支付失败");
            }
        } else if (this._curPayCall) {
            this._curPayCall(1);
        } else {
            const shopItem = Cfg.PayShop.find({
                productId: productId
            });
            
            if (shopItem) {
                Manager.Shop.buyCharge(shopItem);
            } else {
                console.log("[payFinish]查询不到商品:" + productId);
            }
        }
    }
}
