var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SettingController = undefined;
var $2CallID = require("CallID");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2SettingModel = require("SettingModel");
var exp_SettingController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.setup($2SettingModel.default.instance);
    t.changeListener(true);
    t.onSettingInit();
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {
    this._model.reset();
  };
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "SettingController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Setting_IsEnableMusic, this.isEnableMusic, this);
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Setting_IsEnableShake, this.isEnableShake, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Login_Finish, this.loginFinish, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Setting_EnableMusic, this.setEnableMusic, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Setting_EnableAudio, this.setEnableAudio, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Setting_ValueMusic, this.setValueMusic, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Setting_ValueAudio, this.setValueAudio, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Setting_EnableShake, this.setEnableShake, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Setting_OpenView, this.openSettingView, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Activity_OpenExchangeCode, this.openExchangeCodeView, this);
  };
  _ctor.prototype.loginFinish = function () {
    this._model.initSetting();
  };
  _ctor.prototype.onSettingInit = function () {
    $2Manager.Manager.vo.designSize = this._model.getRealDesignSize();
  };
  _ctor.prototype.isEnableMusic = function () {
    return this._model.toggle.music;
  };
  _ctor.prototype.isEnableShake = function () {
    return this._model.toggle.shake;
  };
  _ctor.prototype.setEnableAudio = function (e) {
    this._model.toggle.audio = e;
    $2Manager.Manager.audio.setEnableAudio(e);
    this.mode.save();
  };
  _ctor.prototype.setEnableMusic = function (e) {
    this._model.toggle.music = e;
    $2Manager.Manager.audio.setMusicEnable(e);
    this.mode.save();
  };
  _ctor.prototype.setValueAudio = function (e) {
    this._model.toggle.audioValue = e;
    $2SettingModel.default.instance.toggle.audioValue = e;
    this.mode.save();
  };
  _ctor.prototype.setValueMusic = function (e) {
    this._model.toggle.musicValue = e;
    $2SettingModel.default.instance.toggle.musicValue = e;
    $2Manager.Manager.audio.setMusicVolume(e);
    this.mode.save();
  };
  _ctor.prototype.setEnableShake = function (e) {
    this._model.toggle.shake = e;
    this.mode.save();
  };
  _ctor.prototype.openSettingView = function () {
    $2UIManager.UIManager.Open("ui/setting/SettingView", $2MVC.MVC.openArgs());
  };
  _ctor.prototype.onOpenUserAndPolicy = function (e, t) {
    $2UIManager.UIManager.Open("ui/setting/UserAndPolicyView", $2MVC.MVC.openArgs().setParam({
      title: e,
      url: t
    }));
  };
  _ctor.prototype.openExchangeCodeView = function (e) {
    $2UIManager.UIManager.Open("ui/setting/ExchangeCodeView", $2MVC.MVC.openArgs().setParam({
      isLive: e
    }));
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.SettingController = exp_SettingController;