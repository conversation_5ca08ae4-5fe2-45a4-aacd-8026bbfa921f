import { CallID } from "CallID";
import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { ListenID } from "ListenID";
import { Manager } from "Manager";
import ModeBackpackHeroModel from "ModeBackpackHeroModel";
import RBadgeModel, { RBadge } from "RBadgeModel";
import RB<PERSON>gePoint from "RBadgePoint";

export class RBadgeController extends MVC.MController {
    public look: Set<string> = new Set();

    constructor() {
        super();
        this.setup(RBadgeModel.instance);
        this.changeListener(true);
    }

    public reset(): void {
        this._model.reset();
    }

    public get classname(): string {
        return "RBadgeController";
    }

    public registerAllProtocol(): void {}

    public get BagMode(): any {
        return ModeBackpackHeroModel.instance;
    }

    public changeListener(isAdd: boolean): void {
        Notifier.changeListener(isAdd, ListenID.Login_Finish, this.onLogin_Finish, this, -300);
        Notifier.changeListener(isAdd, ListenID.Badge_Set, this.onBadge_Set, this);
        Notifier.changeListener(isAdd, ListenID.Badge_Add, this.onBadge_Add, this);
        Notifier.changeListener(isAdd, ListenID.Item_GoodsChange, this.onItem_GoodsChange, this);
        Notifier.changeCall(isAdd, CallID.Badge_Get, this.getBadge, this);
    }

    public onLogin_Finish(): void {
        this.mode.onLogin_Finish();
        this.resetPoint();
    }

    public onBadge_Set(node: cc.Node, key: string, id: number): void {
        this.mode.setPoint(node, key, id);
    }

    public onBadge_Add(node: cc.Node, key: string, id: number = 0): void {
        let badgePoint = node.getComByChild(RBadgePoint);
        
        if (badgePoint) {
            badgePoint.resetPoint({
                myKey: key,
                myID: id
            });
        } else if (RBadge.Key[key]) {
            const nodeUuid = node.uuid;
            if (!this.look.has(nodeUuid)) {
                this.look.add(nodeUuid);
                Manager.loader.loadPrefab("ui/common/RBadgePoint").then((prefab: cc.Node) => {
                    if (node.isValid) {
                        this.look.delete(nodeUuid);
                        badgePoint = prefab.getComponent(RBadgePoint);
                        badgePoint.resetPoint({
                            myKey: key,
                            myID: id
                        });
                        prefab.setAttribute({
                            parent: node,
                            x: node.width / 2 - 10,
                            y: node.height / 2 - 10
                        });
                    } else {
                        prefab.destroy();
                    }
                });
            }
        }
    }

    public getBadge(key: string, id: number = 0): any {
        return this.mode.getItem(key, id);
    }

    public onItem_GoodsChange(): void {
        this.resetPoint();
    }

    public resetPoint(): void {
        Notifier.send(ListenID.Badge_Set, RBadge.Key.Shop_FreeCoin, Manager.vo.userVo.dailyData.freeCoin);
        Notifier.send(ListenID.Badge_Set, RBadge.Key.Shop_FreeDiamond, Manager.vo.userVo.dailyData.freeDiamond);
    }
}
