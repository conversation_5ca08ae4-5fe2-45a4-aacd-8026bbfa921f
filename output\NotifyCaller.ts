import { Log } from "Log";
import { CallID } from "CallID";

interface CallData {
    func: Function;
    context: any;
}

export class NotifyCaller {
    private _calls: { [key: number]: CallData } = {};

    public Register(callId: number, callback: Function, context: any): boolean {
        if (callback == null) {
            Log.error("[NotifyCaller].Register:" + callId + " callback null");
            return false;
        }

        const existingCall = this._calls[callId];
        if (existingCall != null) {
            Log.error("[NotifyCaller].Register:" + callId + " register repeat " + existingCall + " " + callback);
            return false;
        }

        this._calls[callId] = {
            func: callback,
            context: context
        };
        return true;
    }

    public Unregister(callId: number, callback: Function, context: any): boolean {
        const callData = this._calls[callId];
        if (callData == null || callData.func !== callback || callData.context !== context) {
            Log.warn("[NotifyCaller].Unregister can't find: " + CallID[callId] + " callback " + callData);
            return false;
        } else {
            delete this._calls[callId];
            return true;
        }
    }

    public Call(callId: number, ...args: any[]): any {
        const callData = this._calls[callId];
        if (callData != null) {
            return callData.func.call(callData.context, ...args);
        }
        Log.error("[NotifyCaller].Call can't find: " + CallID[callId]);
    }
}
