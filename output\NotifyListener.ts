import { Log } from "Log";
import { ListenID } from "ListenID";
import { NotifyID } from "NotifyID";

class Handler {
    private _prior: number = 0;
    private _sendTimes: number = 0;
    private _callback: Function;
    private _context: any;

    constructor(callback: Function, context: any, prior: number) {
        this._callback = callback;
        this._context = context;
        this._prior = prior;
    }

    public get context(): any {
        return this._context;
    }

    public get callback(): Function {
        return this._callback;
    }

    public get prior(): number {
        return this._prior;
    }
}

export class ListenerManager {
    private _sendTimes: number = 0;
    private _listenId: number = 0;
    private _handlers: Handler[] = [];

    constructor(listenId: number) {
        this._listenId = listenId;
        this._handlers = new Array<Handler>();
    }

    public get handlers(): Handler[] {
        return this._handlers;
    }

    public get listenId(): number {
        return this._listenId;
    }

    public toString(): string {
        return "<ListenerManager id:%{m_id}, times:%{m_sendTimes}>";
    }

    public IsExistHandler(callback: Function, context: any): boolean {
        const length = this._handlers.length;
        if (length > 0) {
            for (let i = length - 1; i >= 0; i--) {
                const handler = this._handlers[i];
                if (handler.callback === callback && handler.context === context) {
                    return true;
                }
            }
        }
        return false;
    }

    public RegisterHandler(callback: Function, context: any, prior: number): boolean {
        const handler = new Handler(callback, context, prior);
        const length = this._handlers.length;
        
        if (length > 0) {
            let inserted = false;
            for (let i = length - 1; i >= 0; i--) {
                if (handler.prior >= this._handlers[i].prior) {
                    this._handlers.splice(i + 1, 0, handler);
                    inserted = true;
                    break;
                }
            }
            if (!inserted) {
                this._handlers.unshift(handler);
            }
        } else {
            this._handlers.push(handler);
        }
        return true;
    }

    public RemoveHandler(callback: Function, context: any): boolean {
        let index = -1;
        const length = this._handlers.length;
        
        if (length > 0) {
            for (let i = length - 1; i >= 0; i--) {
                const handler = this._handlers[i];
                if (handler.callback === callback && handler.context === context) {
                    index = i;
                    break;
                }
            }
        }
        
        if (index !== -1) {
            this._handlers.splice(index, 1);
            return true;
        }
        return false;
    }

    public Send(...args: any[]): void {
        const length = this._handlers.length;
        for (let i = length - 1; i >= 0; i--) {
            const handler = this._handlers[i];
            handler.callback.call(handler.context, ...args);
        }
    }
}

export class NotifyListener {
    private _managers: { [key: number]: ListenerManager } = {};
    private _callStacks: number[] = [];

    public GetCellStackString(): string {
        let result = "[";
        for (const stackId of this._callStacks) {
            result += stackId + ",";
        }
        return result + "]";
    }

    public CheckAndPushCallStack(listenId: number): boolean {
        const stackLength = this._callStacks.length;
        if (stackLength >= 15) {
            cc.error("[NotifyListener].Send out call stack:" + this.GetCellStackString() + " msg:" + listenId);
            return false;
        } else if (stackLength >= 10) {
            cc.warn("[NotifyListener].Send warning call stack:" + this.GetCellStackString() + " msg:" + listenId);
            return false;
        } else {
            this._callStacks.push(listenId);
            return true;
        }
    }

    public PopCallStack(): void {
        this._callStacks.pop();
    }

    public Register(listenId: number, callback: Function, context: any, prior?: number): void {
        if (callback != null) {
            let manager = this._managers[listenId];
            if (manager == null) {
                manager = new ListenerManager(listenId);
                this._managers[listenId] = manager;
            } else if (manager.IsExistHandler(callback, context)) {
                Log.error("[NotifyListener].Register:" + (ListenID[listenId] || NotifyID[listenId]) + " callback repeat, skip " + context);
                return;
            }
            manager.RegisterHandler(callback, context, prior);
        } else {
            Log.error("[NotifyListener].Register:" + listenId + " callback null");
        }
    }

    public Unregister(listenId: number, callback: Function, context: any): void {
        const manager = this._managers[listenId];
        if (manager != null) {
            if (!manager.RemoveHandler(callback, context)) {
                Log.warn("[NotifyListener].Unregister:" + listenId + " can't find callback:" + callback);
            }
            if (manager.handlers.length === 0) {
                delete this._managers[manager.listenId];
            }
        } else {
            Log.warn("[NotifyListener].Unregister can't find ListenerManager:" + listenId + " callback:" + callback);
        }
    }

    public Send(listenId: number, ...args: any[]): void {
        const manager = this._managers[listenId];
        if (manager != null && this.CheckAndPushCallStack(listenId)) {
            manager.Send(...args);
            this.PopCallStack();
        }
    }

    public IsExist(listenId: number): boolean {
        return this._managers[listenId] != null;
    }
}
