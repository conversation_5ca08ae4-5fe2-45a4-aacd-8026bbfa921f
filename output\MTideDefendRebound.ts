import { Cfg } from "./Cfg";
import { Notifier } from "./Notifier";
import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { FPolygonCollider } from "./FPolygonCollider";
import { Intersection } from "./Intersection";
import { GameUtil } from "./GameUtil";
import { RecordVo } from "./RecordVo";
import { Game } from "./Game";
import { BronMonsterManger } from "./BronMonsterManger";
import { CompManager } from "./CompManager";
import { M33_TestBox } from "./M33_TestBox";
import { NodePool } from "./NodePool";
import { TideDefendModel } from "./TideDefendModel";
import { MonstarTideDragon } from "./MonstarTideDragon";
import { MTideDefendRmod } from "./MTideDefendRmod";

export namespace MTideDefendRebound {
    export enum RoundStatus {
        NONE = 0,
        BATTLE = 1,
        ONTICK = 2,
        SELECTEQUIP = 3,
        END = 4
    }

    export enum TideDefendDragonType {
        DRAGON1 = 999999991,
        DRAGON2 = 999999992,
        DRAGON3 = 999999993,
        DRAGON4 = 999999910
    }

    export enum PassType {
        Forward = 0,
        D360 = 1,
        Move = 2
    }

    export enum poolType {
        NormalBuff = 1,
        HighBuff = 2
    }

    export class RecordData extends RecordVo.Data {
        public poolADMap: any = {
            [poolType.NormalBuff]: {
                resetNum: 3,
                getAll: 1
            },
            [poolType.HighBuff]: {
                resetNum: 1,
                getAll: 0
            }
        };
        public freeTime: number = 1;
        public killNum: number = 0;
        public countdownTime: number = 0;
        public adRefreshEquip: number = 9999;
        public ADNum: number = 0;
        public testNum: number = 1;
    }

    export class Mgr extends Game.Mgr {
        public cameraZoomRatio: number = 1;
        public recordVo: RecordVo.Mgr;
        public passType: PassType = PassType.Forward;
        public bossSkilling: Set<any> = new Set();
        public bossHp: number = 9999;
        public passParam: any;
        private _mainRole: MTideDefendRmod;

        constructor(passParam: any) {
            super(passParam);
            this.recordVo = new RecordVo.Mgr("MTideDefendRebound", () => {
                return new RecordData();
            });
            this.passParam = passParam;
        }

        get mode(): TideDefendModel {
            return TideDefendModel.default.instance;
        }

        get rVo(): RecordData {
            return this.recordVo.vo as RecordData;
        }

        public loadMap(gameNode: cc.Node, finishCall?: Function): void {
            this.gameNode = gameNode;
            this.miniGameCfg = Cfg.MiniGameLv.get(this.passParam.id);
            this.mode.miniGameCfg = this.miniGameCfg;
            this._entityNode = gameNode.getChildByName("entityNode");
            this._mapNode = gameNode.getChildByName("mapNode");
            this._bulletNode = gameNode.getChildByName("bulletNode").setAttribute({
                zIndex: 500
            });
            this._topEffectNode = gameNode.getChildByName("topEffectNode").setAttribute({
                zIndex: 1000
            });
            this.LifeBarUI = gameNode.getORaddChildByName("LifeBarUI").setAttribute({
                zIndex: 1001
            });
            this.topUINode = gameNode.getORaddChildByName("topUINode").setAttribute({
                zIndex: 1002
            });
            this._botEffectNode = gameNode.getChildByName("botEffectNode").setAttribute({
                zIndex: 1003
            });
            this.behitUI = gameNode.getORaddChildByName("behitUI");
            this._finishCall = finishCall;

            Manager.loader.loadPrefab(this.miniGameCfg.lvPrefab).then((mapNode: cc.Node) => {
                mapNode.setAttribute({
                    parent: this._mapNode,
                    zIndex: -1
                });
                this.myTiledMap = this._mapNode.getComByChild(cc.TiledMap);
                this.createRole(cc.v2(0, 90), this.miniGameCfg.roleId).then(() => {});
                this.gameCamera.lookPos.set(cc.v2(0, GameUtil.getDesignSize.height / 2));
                this.sendEvent("Start");
            });

            this.gameCamera.setZoomRatio(this.cameraZoomRatio);
            this.gameCamera.setPosition(cc.Vec2.ZERO);
            this.bronMonsterMgr = this.gameNode.getORaddComponent(SpawningMgr);
            this.bronMonsterMgr.init();
            this.topUINode.active = !!this.mode.bShowMonsterBlob(this.miniGameCfg);
            
            if (this.mode.bShowMatrix(this.miniGameCfg)) {
                Manager.setGroupMatrixByStr("Monsetr", "Role", true);
            }
            
            this.scenceSize = [
                -375 / this.gameCamera.cutZoomRatio * 0.8,
                375 / this.gameCamera.cutZoomRatio * 0.8,
                0,
                0
            ];
            this._finishCall && this._finishCall();
        }

        public getObjPos(obj: any): cc.Vec2 {
            return cc.v2(
                obj.x - this.myTiledMap.node.width / 2 + obj.width / 2,
                obj.y - this.myTiledMap.node.height / 2 - obj.height
            );
        }

        public gameEnd(): void {
            Game.timerOnce(() => {
                this.gameState = Game.State.NONE;
            }, 0.5);
        }

        public createRole(position: cc.Vec2, roleId?: number): Promise<MTideDefendRmod> {
            return new Promise((resolve) => {
                NodePool.spawn("entity/fight/ModeTideDefend/roleMod").setNodeAssetFinishCall((roleNode: cc.Node) => {
                    const role = roleNode.getComponent(MTideDefendRmod.default);
                    roleNode.parent = this._topEffectNode;
                    role.setPosition(position);
                    role.init();
                    role._logicTime = 0;
                    if (roleId) {
                        role.roleId = roleId;
                    }
                    role.setRole();
                    CompManager.default.Instance.registerComp(role);
                    this.mainRole = role;
                    resolve(role);
                });
            });
        }

        public gamePause(isPause: boolean): void {
            super.gamePause(isPause);
            Manager.setPhysics(!isPause);
        }

        public onUpdate(dt: number): void {
            super.onUpdate(dt);
        }

        get mainRole(): MTideDefendRmod {
            return this._mainRole;
        }

        set mainRole(role: MTideDefendRmod) {
            this._mainRole = role;
        }

        public checkBossSkill(): void {
            if (this.bossSkilling.size > 0) return;

            const skills: any[] = [];
            const skillCount = this.bossDiff.find(diff => 
                this.bossHp >= diff[2] && this.bossHp < diff[3]
            )?.[4] || 1;

            this.monsterMap.forEach((monster: any) => {
                if (monster instanceof MonstarTideDragon.default) {
                    skills.push(...monster.skillMgr.skills.filter((skill: any) => skill.isReady));
                }
            });

            GameUtil.getRandomInArray(skills, skillCount).forEach((skill: any) => {
                skill.mgr.use(skill.skillCfg.id);
            });
        }

        public setTestMode(isOpen: boolean): void {
            if (this._mapTestMode) {
                this._mapTestMode.open(isOpen);
            } else {
                Manager.loader.loadPrefab("ui/ModeChains/M33_TestBox").then((testBoxNode: cc.Node) => {
                    testBoxNode.setParent(this.gameNode);
                    this._mapTestMode = testBoxNode.getORaddComponent(M33_TestBox.default);
                });
            }
        }
    }

    const aimVector = cc.v2();
    export const maxPower = 200;

    export class KnifeController extends GameSeting.CompBase {
        public bulletNum: number = 4;
        public line: cc.Sprite;
        private _aimData: any;

        constructor() {
            super();
            this.line = this.game.botEffectNode.getComByChild(cc.Sprite, "line");
        }

        get game(): Mgr {
            return Game.mgr as Mgr;
        }

        get aimData(): any {
            return this._aimData;
        }

        set aimData(data: any) {
            this._aimData = data;
            
            if (!this.line.node.active) {
                this.ower.setAnimation("aim", true);
            }

            const startPos = this.ower.bodyPosition;
            const endPos = GameUtil.AngleAndLenToPos((data.angle + 180) % 360, 2000).add(this.ower.bodyPosition);
            const intersections: any[] = [];

            this.game.myTiledMap.getComponent(FPolygonCollider.default).points = [];
            this.game.myTiledMap.getComponents(cc.PhysicsPolygonCollider).forEach((collider: cc.PhysicsPolygonCollider) => {
                collider.points.forEach((point: cc.Vec2, index: number) => {
                    const nextPoint = collider.points[index + 1] || collider.points[0];
                    const intersection = Intersection.getLineSegmentIntersection(startPos, endPos, point, nextPoint);
                    if (intersection) {
                        intersections.push({
                            pos: intersection,
                            d: cc.Vec2.squaredDistance(startPos, intersection)
                        });
                    }
                    this.game.myTiledMap.getComponent(FPolygonCollider.default).points.push(point);
                }

    export class SpawningMgr extends BronMonsterManger {
        private _batchNum: number = 0;
        public buffOffset: number = 0;
        public haveLife: boolean = false;
        public cutStatus: RoundStatus = RoundStatus.NONE;
        public reTime: number = 1;
        public MonsterType: any = {
            1: "MonsterTideDefend",
            2: "MonsterTideDefend",
            3: "MonsterTideDefend",
            4: "MonsterTideDefend"
        };
        public countDown: number = 0;
        public addOnce: boolean = false;
        public dragonIndex: number = 0;

        get batchNum(): number {
            return this._batchNum;
        }

        set batchNum(value: number) {
            this._batchNum = value;
            if (value !== 0) {
                Notifier.send(ListenID.Fight_GameRound, value);
                this._batchSumCount = 0;
                this.RoundMonster = this.MonsterLv.filter((monster: any) => monster.round === this._batchNum);
                this.RoundMonster.forEach((monster: any) => {
                    this._batchSumCount += monster.sumCount;
                });
                Notifier.send(ListenID.Fight_GameRound, value);
            }
        }

        get game(): Mgr {
            return Game.mgr as Mgr;
        }

        get mode(): TideDefendModel {
            return TideDefendModel.default.instance;
        }

        public init(): void {
            this.MonsterLv = JSON.parse(JSON.stringify(Game.ModeCfg.MonsterLv.filter({
                lv: this.game.miniGameCfg.lvid
            })));
            this.batchNum++;
            this.haveLife = false;
        }

        get curBullet(): any {
            return this.game.curBullet;
        }

        public onUpdate(dt: number): void {
            if (this.game.gameState !== Game.State.PAUSE) {
                if (this.cutStatus === RoundStatus.BATTLE) {
                    this._dtTime += dt;
                    if (this._dtTime >= 1) {
                        this._dtTime = 0;
                    }
                    if (this._batchSumCount > 0) {
                        this.checkAddMonster(dt);
                    }
                    this.checkMonDe();
                }
                this.reTime = GameUtil.random(1, 100);
            }
        }

        get survivalMonsterNum(): number {
            return this.game.monsterMap.size;
        }

        public checkMonDe(): void {
            if (this._batchSumCount <= 0 && this.survivalMonsterNum === 0) {
                Notifier.send(ListenID.Fight_End, true);
            }
        }

        get role(): MTideDefendRmod {
            return this.game.mainRole;
        }

        public changeListener(listener: boolean): void {
            super.changeListener(listener);
        }

        public changeGameStatus(status: RoundStatus): void {
            switch (status) {
                case RoundStatus.BATTLE:
                    break;
            }
            Notifier.send(ListenID.Fight_GameRoundType, status);
            this.cutStatus = status;
        }

        get maxLen(): number {
            return GameUtil.getDesignSize.height / this.game.gameCamera.cutZoomRatio;
        }

        get randomL(): number {
            let range = 100;
            if (this.mode.miniGameCfg && this.mode.miniGameCfg.randomL) {
                range = this.mode.miniGameCfg.randomL;
            }
            return Game.random(this.maxLen + 200, this.maxLen + 200 + range);
        }

        get randomPos(): cc.Vec2 {
            return cc.v2(
                Game.random(this.game.scenceSize[0], this.game.scenceSize[1]),
                this.randomL
            );
        }

        public getMonSpPos(monsterCfg: any, index: number = 0): cc.Vec2 {
            let yPos = this.randomL;
            const bagMonsterCfg = Cfg.bagMonsterLv.get(monsterCfg.id);
            const screenWidth = cc.winSize.width;
            const halfWidth = screenWidth / 2;
            let xPos = halfWidth;
            const centerOffset = screenWidth / 2;

            if (bagMonsterCfg && bagMonsterCfg.bronArea && bagMonsterCfg.bronArea.length) {
                const areaIndex = this.reTime % 2;
                const area = bagMonsterCfg.bronArea[areaIndex];
                xPos = Game.random(area[0] - centerOffset, area[1] - centerOffset);

                if (bagMonsterCfg.bronMatrix) {
                    xPos = 0;
                    yPos = this.maxLen;
                    const matrix = bagMonsterCfg.bronMatrix;
                    if (matrix[index - 1]) {
                        return cc.v2(xPos + 100 * matrix[index - 1][0], yPos + 100 * matrix[index - 1][1]);
                    } else {
                        return cc.v2(xPos, yPos);
                    }
                }
            } else {
                xPos = GameUtil.random(-halfWidth, halfWidth);
            }
            return cc.v2(xPos, yPos);
        }

        public checkAddMonster(dt: number): void {
            if (this.mainRole && !this.mainRole.isDead && !this.isBossRound) {
                this._RoundMonster.forEach((monster: any) => {
                    monster.letTime += dt;
                    if (monster.createNum < monster.sumCount && monster.letTime > monster.bronTime) {
                        monster.letTime = 0;
                        monster.createNum++;
                        if (monster.Count > 200) {
                            Notifier.send(ListenID.Fight_ShowGameTips, 3, "尸潮来袭");
                        }
                        if (this.bDrongin(monster)) {
                            this.addDragon(monster);
                            this._batchSumCount--;
                        } else {
                            this.addMonsterById(monster, null);
                        }
                    }
                });
            }
        }

        public addDragon(monsterCfg: any): void {
            const dragonPaths: any = {};
            const dragonId = monsterCfg.monId ? monsterCfg.monId[0] : 999999991;

            dragonPaths[999999991] = [
                { x: -477, y: 1808 }, { x: -156, y: 1156 }, { x: -303, y: 945 },
                { x: -173, y: 684 }, { x: -273, y: 480 }, { x: -150, y: 280 },
                { x: -288, y: 51 }, { x: -183, y: -151 }, { x: -298, y: -380 },
                { x: -174, y: -621 }, { x: -326, y: -948 }, { x: -292, y: -1269 },
                { x: -1236, y: -1601 }
            ];

            dragonPaths[999999992] = [
                { x: 23, y: 1781 }, { x: 47, y: 1198 }, { x: -57, y: 954 },
                { x: 14, y: 693 }, { x: -82, y: 484 }, { x: 37, y: 242 },
                { x: -105, y: 10 }, { x: -8, y: -234 }, { x: -136, y: -430 },
                { x: -12, y: -717 }, { x: -172, y: -927 }, { x: -155, y: -1356 },
                { x: -1236, y: -1601 }
            ];

            dragonPaths[999999993] = [
                { x: 355, y: 1665 }, { x: 215, y: 1457 }, { x: 367, y: 1243 },
                { x: 237, y: 1000 }, { x: 396, y: 722 }, { x: 255, y: 515 },
                { x: 426, y: 263 }, { x: 306, y: -5 }, { x: 436, y: -267 },
                { x: 310, y: -545 }, { x: 443, y: -900 }, { x: 172, y: -1330 },
                { x: 91, y: -2493 }
            ];

            dragonPaths[999999910] = [
                { x: -678, y: 1100 }, { x: -294, y: 1100 }, { x: 404, y: 1100 },
                { x: 412, y: 1000 }, { x: -403, y: 1000 }, { x: -392, y: 900 },
                { x: 399, y: 900 }, { x: 405, y: 800 }, { x: -406, y: 800 },
                { x: -424, y: 700 }, { x: 396, y: 700 }, { x: 400, y: 600 },
                { x: -409, y: 600 }, { x: -411, y: 500 }, { x: 414, y: 500 },
                { x: 394, y: 400 }, { x: -405, y: 400 }, { x: -383, y: 300 },
                { x: 0, y: 300 }, { x: 0, y: -1454 }
            ];

            this.addDragonByindex([dragonPaths[dragonId]], monsterCfg);
        }

        public addDragonByindex(paths: any[], monsterCfg: any): void {
            if (paths && paths[0]) {
                this.createDragon(paths, this.dragonIndex, monsterCfg);
                console.log("this.dragonIndex: ", this.dragonIndex);
                this.dragonIndex++;
            } else {
                console.log("路径为空");
            }
        }

        public createDragon(paths: any[], index: number, monsterCfg: any): Promise<MonstarTideDragon> {
            return new Promise((resolve) => {
                const chainsContainer = this.game.gameNode.getORaddChildByName("Chains_" + index).getORaddChildByName("Chains");
                chainsContainer.setAttribute({
                    zIndex: 300,
                    x: paths[0][0].x,
                    y: paths[0][0].y
                });

                const dragon = chainsContainer.addComponent(MonstarTideDragon.default);
                dragon.init();
                dragon.baglvCfg = monsterCfg;
                dragon.setInfo(index, paths);
                CompManager.default.Instance.registerComp(dragon);
                this.game.chainsList.push(dragon);
                resolve(dragon);
            });
        }

        public addMonsterById(monsterCfg: any, position?: cc.Vec2, delay: number = 0): void {
            let count = monsterCfg.Count;
            Game.timer(() => {
                const bagMonsterCfg = Cfg.bagMonsterLv.get(monsterCfg.id);
                if (bagMonsterCfg && bagMonsterCfg.bronArea && bagMonsterCfg.bronArea.length) {
                    count--;
                    position = this.getMonSpPos(monsterCfg, monsterCfg.Count - count);
                }
                this.createMonster(monsterCfg, position || this.randomPos).then((monster: any) => {
                    monster.toMove();
                });
            }, monsterCfg.bronSpeed, monsterCfg.Count);
            this._batchSumCount--;
        }

        public getMonsterSetType(monsterCfg: any): string {
            if (this.bDrongin(monsterCfg)) {
                return "ModeTideDefend/MonstarTideDragon";
            } else {
                let monsterType: string;
                if (monsterCfg.dropExpRatio) {
                    monsterType = "MonsterTideDefend";
                } else if (monsterCfg.buffList) {
                    if (monsterCfg.buffList.length === 1) {
                        monsterType = "ModeTideDefend/MonstarRailingSingle";
                    } else if (monsterCfg.buffList.length === 2) {
                        monsterType = "ModeTideDefend/MonstarRailing";
                    }
                } else {
                    monsterType = "MonsterTideDefend";
                }

                if (monsterType) {
                    return NodePool.spawn("entity/fight/" + monsterType);
                }
                return monsterType;
            }
        }

        public bDrongin(monsterCfg: any): boolean {
            return Cfg.Monster.get(monsterCfg.monId[0]).type === 10;
        }
    }
});
            });

            intersections.sort((a, b) => a.d - b.d);
            if (intersections[0]) {
                endPos.set(intersections[0].pos);
            }

            this.line.node.setAttribute({
                active: true,
                position: this.ower.bodyPosition,
                angle: (data.angle + 180) % 360,
                height: cc.Vec2.distance(startPos, endPos)
            });
        }
    }
