import { Manager } from "./Manager";
import { <PERSON> } from "./<PERSON>";

const { ccclass } = cc._decorator;

@ccclass
class MonsterElite extends Monster {
    public init(): void {
        super.init();
        const outlineWidth = [2].includes(this.monCfg.type) ? 
            0.03 * Manager.vo.switchVo.shardAdapter : 0;
        this._mals.setProperty("outlineWidth", outlineWidth);
    }

    public isOffScreen(): boolean {
        return false;
    }

    public materialTwinkle(): void {
        if (this._mals) {
            let whiteValue = 0.5;
            this._mals.setProperty("setwhite", whiteValue);
            this.schedule(() => {
                if (this.isValid) {
                    whiteValue -= 0.05;
                    this._mals.setProperty("setwhite", Math.max(0, whiteValue));
                }
            }, 0, 30);
        }
    }
}

export default MonsterElite;
