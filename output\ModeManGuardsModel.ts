import { MVC } from "./MVC";
import { Game } from "./Game";

class ModeManGuardsModel extends MVC.BaseModel {
    private static _instance: ModeManGuardsModel = null;
    public gameMode: Game.Mode = Game.Mode.MANGUARDS;

    constructor() {
        super();
        if (!ModeManGuardsModel._instance) {
            ModeManGuardsModel._instance = this;
        }
    }

    public reset(): void {}

    static get instance(): ModeManGuardsModel {
        if (!ModeManGuardsModel._instance) {
            ModeManGuardsModel._instance = new ModeManGuardsModel();
        }
        return ModeManGuardsModel._instance;
    }
}

export default ModeManGuardsModel;
