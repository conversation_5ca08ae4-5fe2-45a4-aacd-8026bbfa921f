import LocalStorage from "LocalStorage";

class ReportQueue {
    private _list: any[] = [];
    private _storageKey: string = "sdk_report_list";

    constructor() {
        const storedData = LocalStorage.getItem(this._storageKey);
        this._list = storedData ? JSON.parse(storedData) : [];
    }

    public push(item: any): number {
        const length = this._list.push(item);
        this.refeshStorage();
        return length;
    }

    public pop(): any {
        const item = this._list.shift();
        this.refeshStorage();
        return item;
    }

    public range(start: number, count: number): any[] {
        const items = this._list.splice(start, count);
        this.refeshStorage();
        return items;
    }

    private refeshStorage(): boolean {
        const data = JSON.stringify(this._list);
        return LocalStorage.setItem(this._storageKey, data);
    }

    public len(): number {
        return this._list.length;
    }

    public getList(): any[] {
        return this._list;
    }
}

export default new ReportQueue();
