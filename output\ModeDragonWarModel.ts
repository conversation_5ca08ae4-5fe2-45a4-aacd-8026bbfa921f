import { Cfg } from "./Cfg";
import { MVC } from "./MVC";
import { Game } from "./Game";

class ModeDragonWarModel extends MVC.BaseModel {
    private static _instance: ModeDragonWarModel;
    public gameMode: Game.Mode = Game.Mode.DRAGONWAR;
    public roleConfig: any = Cfg.RoleUnlock.get(30800);
    public bgPath: string;
    public battleBgPath: string;
    public pathList: any[];
    public wallConfigs: any[];

    static get instance(): ModeDragonWarModel {
        if (!ModeDragonWarModel._instance) {
            ModeDragonWarModel._instance = new ModeDragonWarModel();
        }
        return ModeDragonWarModel._instance;
    }

    public initMonsterConfig(levelId: number): void {
        const miniGameCfg = Cfg.MiniGameLv.get(levelId);
        const prefabPaths = miniGameCfg.lvPrefab.split("|");
        this.bgPath = prefabPaths[0];
        this.battleBgPath = prefabPaths[1];
        
        const lvid = miniGameCfg.lvid;
        this.pathList = JSON.parse(miniGameCfg.path);
        
        const monsterConfigs = Cfg.bagMonsterLv.filter({
            lv: lvid
        });
        this.wallConfigs = monsterConfigs;
    }

    public reset(): void {}
}

export default ModeDragonWarModel;
